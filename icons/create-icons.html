<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        canvas {
            display: block;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <h1>AI Website Redesigner - Icon Generator</h1>
    <p>Click the buttons below to download the extension icons:</p>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon16" width="16" height="16"></canvas>
        </div>
        <button onclick="downloadIcon('icon16', 16)">Download 16x16</button>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon32" width="32" height="32"></canvas>
        </div>
        <button onclick="downloadIcon('icon32', 32)">Download 32x32</button>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon48" width="48" height="48"></canvas>
        </div>
        <button onclick="downloadIcon('icon48', 48)">Download 48x48</button>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <canvas id="icon128" width="128" height="128"></canvas>
        </div>
        <button onclick="downloadIcon('icon128', 128)">Download 128x128</button>
    </div>

    <div style="text-align: center; margin: 20px;">
        <button onclick="downloadAllIcons()" style="background: #28a745; font-size: 16px; padding: 12px 24px;">
            Download All Icons
        </button>
    </div>

    <script>
        // Create icons for different sizes
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, size, size);

            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');

            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();

            // Draw magic wand icon
            ctx.strokeStyle = '#ffffff';
            ctx.fillStyle = '#ffffff';
            ctx.lineWidth = Math.max(1, size / 16);

            const centerX = size / 2;
            const centerY = size / 2;
            const scale = size / 48; // Base scale for 48px icon

            // Wand stick
            ctx.beginPath();
            ctx.moveTo(centerX - 8 * scale, centerY + 8 * scale);
            ctx.lineTo(centerX + 8 * scale, centerY - 8 * scale);
            ctx.stroke();

            // Wand tip (star)
            drawStar(ctx, centerX + 6 * scale, centerY - 6 * scale, 4 * scale, 5);

            // Magic sparkles
            drawStar(ctx, centerX - 4 * scale, centerY - 2 * scale, 2 * scale, 4);
            drawStar(ctx, centerX + 2 * scale, centerY + 4 * scale, 1.5 * scale, 4);
            drawStar(ctx, centerX - 6 * scale, centerY + 2 * scale, 1 * scale, 4);
        }

        function drawStar(ctx, x, y, radius, points) {
            const angle = Math.PI / points;
            ctx.beginPath();
            for (let i = 0; i < 2 * points; i++) {
                const r = i % 2 === 0 ? radius : radius * 0.5;
                const a = i * angle;
                const px = x + Math.cos(a) * r;
                const py = y + Math.sin(a) * r;
                if (i === 0) {
                    ctx.moveTo(px, py);
                } else {
                    ctx.lineTo(px, py);
                }
            }
            ctx.closePath();
            ctx.fill();
        }

        function downloadIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAllIcons() {
            setTimeout(() => downloadIcon('icon16', 16), 100);
            setTimeout(() => downloadIcon('icon32', 32), 200);
            setTimeout(() => downloadIcon('icon48', 48), 300);
            setTimeout(() => downloadIcon('icon128', 128), 400);
        }

        // Generate all icons on page load
        window.onload = function() {
            createIcon('icon16', 16);
            createIcon('icon32', 32);
            createIcon('icon48', 48);
            createIcon('icon128', 128);
        };
    </script>
</body>
</html>
