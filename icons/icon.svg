<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="63" fill="url(#bg)"/>
  
  <!-- Magic wand -->
  <line x1="42" y1="86" x2="86" y2="42" stroke="white" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Wand tip star -->
  <polygon points="86,32 89,39 96,39 91,44 93,51 86,47 79,51 81,44 76,39 83,39" fill="white"/>
  
  <!-- Sparkles -->
  <polygon points="52,56 54,60 58,60 55,63 56,67 52,65 48,67 49,63 46,60 50,60" fill="white"/>
  <polygon points="74,78 76,81 79,81 77,83 78,86 74,84 70,86 71,83 69,81 72,81" fill="white"/>
  <polygon points="46,72 47,74 49,74 48,75 48,77 46,76 44,77 44,75 43,74 45,74" fill="white"/>
</svg>
