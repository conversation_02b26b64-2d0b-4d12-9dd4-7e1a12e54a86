// Simple script to generate base64 PNG icons for the Chrome extension
// Run this in a browser console or Node.js environment with canvas support

const fs = require('fs');
const { createCanvas } = require('canvas');

function createIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');

    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');

    // Draw background circle
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
    ctx.fill();

    // Draw magic wand icon
    ctx.strokeStyle = '#ffffff';
    ctx.fillStyle = '#ffffff';
    ctx.lineWidth = Math.max(1, size / 16);

    const centerX = size / 2;
    const centerY = size / 2;
    const scale = size / 48;

    // Wand stick
    ctx.beginPath();
    ctx.moveTo(centerX - 8 * scale, centerY + 8 * scale);
    ctx.lineTo(centerX + 8 * scale, centerY - 8 * scale);
    ctx.stroke();

    // Wand tip (star)
    drawStar(ctx, centerX + 6 * scale, centerY - 6 * scale, 4 * scale, 5);

    // Magic sparkles
    drawStar(ctx, centerX - 4 * scale, centerY - 2 * scale, 2 * scale, 4);
    drawStar(ctx, centerX + 2 * scale, centerY + 4 * scale, 1.5 * scale, 4);

    return canvas;
}

function drawStar(ctx, x, y, radius, points) {
    const angle = Math.PI / points;
    ctx.beginPath();
    for (let i = 0; i < 2 * points; i++) {
        const r = i % 2 === 0 ? radius : radius * 0.5;
        const a = i * angle;
        const px = x + Math.cos(a) * r;
        const py = y + Math.sin(a) * r;
        if (i === 0) {
            ctx.moveTo(px, py);
        } else {
            ctx.lineTo(px, py);
        }
    }
    ctx.closePath();
    ctx.fill();
}

// Generate icons
const sizes = [16, 32, 48, 128];
sizes.forEach(size => {
    const canvas = createIcon(size);
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(`icon${size}.png`, buffer);
    console.log(`Generated icon${size}.png`);
});

console.log('All icons generated successfully!');