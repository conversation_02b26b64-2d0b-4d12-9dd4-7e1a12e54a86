<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Gemini API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input {
            width: 300px;
        }
        button {
            background: #4285f4;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #3367d6;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Gemini API Test</h1>
        <p>This will help us debug the API key issue.</p>
        
        <div>
            <input type="password" id="apiKey" placeholder="Enter your Gemini API key">
            <button onclick="testAPI()">Test API</button>
            <button onclick="listModels()">List Models</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        async function listModels() {
            const apiKey = document.getElementById('apiKey').value;
            const resultDiv = document.getElementById('result');
            
            if (!apiKey) {
                resultDiv.innerHTML = '<div class="error">Please enter your API key first</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div>Listing available models...</div>';
                
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }
                
                const data = await response.json();
                const models = data.models || [];
                
                let html = '<div class="success"><h3>Available Models:</h3>';
                models.forEach(model => {
                    html += `<div>• ${model.name} - ${model.displayName || 'No display name'}</div>`;
                });
                html += '</div>';
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testAPI() {
            const apiKey = document.getElementById('apiKey').value;
            const resultDiv = document.getElementById('result');
            
            if (!apiKey) {
                resultDiv.innerHTML = '<div class="error">Please enter your API key first</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div>Testing API key...</div>';
                
                // Test with gemini-1.5-flash first (most likely to work)
                const models = ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-pro'];
                
                for (const model of models) {
                    try {
                        console.log(`Testing model: ${model}`);
                        
                        const requestBody = {
                            contents: [{
                                parts: [{
                                    text: 'Hello! Please respond with "API test successful".'
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.1,
                                maxOutputTokens: 50
                            }
                        };
                        
                        const response = await fetch(
                            `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`,
                            {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(requestBody)
                            }
                        );
                        
                        if (response.ok) {
                            const data = await response.json();
                            const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text || 'No response text';
                            
                            resultDiv.innerHTML = `
                                <div class="success">
                                    <h3>✅ API Key Works!</h3>
                                    <p><strong>Working Model:</strong> ${model}</p>
                                    <p><strong>Response:</strong> ${responseText}</p>
                                </div>
                            `;
                            return; // Success, exit the loop
                        } else {
                            console.log(`Model ${model} failed:`, await response.text());
                        }
                        
                    } catch (modelError) {
                        console.log(`Model ${model} error:`, modelError);
                    }
                }
                
                // If we get here, all models failed
                resultDiv.innerHTML = '<div class="error">All models failed. Please check your API key and try listing models first.</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
