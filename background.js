// Background script for AI Website Redesigner Chrome Extension

// Gemini API configuration
const MODEL_ID = 'gemini-1.5-flash-latest';  // Using available model
const GENERATE_CONTENT_API = 'streamGenerateContent';
const GEMINI_API_BASE_URL = `https://generativelanguage.googleapis.com/v1beta/models/${MODEL_ID}:${GENERATE_CONTENT_API}`;

// List available models (for debugging)
async function listAvailableModels(apiKey) {
    try {
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`);
        if (!response.ok) {
            throw new Error(`Failed to list models: ${response.status}`);
        }
        const data = await response.json();
        console.log('Available models:', data.models?.map(m => m.name) || []);
        return data.models || [];
    } catch (error) {
        console.error('Error listing models:', error);
        return [];
    }
}

// Handle streaming response from Gemini API
async function handleStreamingResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.trim() === '') continue;
                if (line.startsWith('data: ')) {
                    try {
                        const jsonData = JSON.parse(line.slice(6));
                        if (jsonData.candidates && jsonData.candidates[0] && jsonData.candidates[0].content) {
                            const parts = jsonData.candidates[0].content.parts;
                            if (parts && parts[0] && parts[0].text) {
                                fullResponse += parts[0].text;
                            }
                        }
                    } catch (parseError) {
                        console.warn('Failed to parse streaming chunk:', parseError);
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error reading streaming response:', error);
        throw error;
    } finally {
        reader.releaseLock();
    }

    return fullResponse;
}

// Message listener
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    switch (message.action) {
        case 'testApiKey':
            testGeminiApiKey(message.apiKey)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response

        case 'redesignWithGemini':
            redesignWithGemini(message.apiKey, message.html, message.prompt, message.options)
                .then(result => sendResponse(result))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // Keep message channel open for async response

        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

// Test Gemini API key
async function testGeminiApiKey(apiKey) {
    return {
        success: true,
        message: 'API key is valid (dummy response)'
    };
}

// Redesign website with Gemini
async function redesignWithGemini(apiKey, html, prompt, options = {}) {
    return {
        success: true,
        redesignedHTML: '<html><body><h1>Redesigned (dummy response)</h1></body></html>',
        changes: 'Website redesigned with AI improvements (dummy response)',
        originalPrompt: prompt
    };
}

// Clean HTML for processing
function cleanHTML(html) {
    // Remove scripts and potentially harmful content
    let cleaned = html
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<noscript\b[^<]*(?:(?!<\/noscript>)<[^<]*)*<\/noscript>/gi, '')
        .replace(/on\w+="[^"]*"/gi, '') // Remove inline event handlers
        .replace(/javascript:/gi, ''); // Remove javascript: URLs
    
    // Limit size if too large (Gemini has token limits)
    if (cleaned.length > 50000) {
        // Try to preserve important parts
        const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*)<\/body>/i);
        if (bodyMatch) {
            const bodyContent = bodyMatch[1];
            if (bodyContent.length > 40000) {
                cleaned = `<html><head><title>Website</title></head><body>${bodyContent.substring(0, 40000)}...</body></html>`;
            }
        } else {
            cleaned = cleaned.substring(0, 50000) + '...';
        }
    }
    
    return cleaned;
}

// Create redesign prompt for Gemini
function createRedesignPrompt(html, userPrompt, options) {
    const preserveContent = options.preserveContent !== false;
    const responsiveDesign = options.responsiveDesign !== false;

    return `You are an expert web designer and developer with access to current design trends and best practices. I need you to redesign the following HTML webpage based on the user's requirements.

USER REQUIREMENTS:
${userPrompt}

DESIGN CONSTRAINTS:
- ${preserveContent ? 'PRESERVE all original text content and functionality' : 'You may modify content as needed'}
- ${responsiveDesign ? 'ENSURE the design is fully responsive and mobile-friendly' : 'Focus on desktop design'}
- Use modern CSS techniques (Flexbox, Grid, CSS Variables, modern color schemes)
- Apply current design trends and best practices (you can search for latest trends if needed)
- Improve typography, spacing, and visual hierarchy
- Ensure excellent accessibility practices (WCAG compliance)
- Keep all existing functionality intact (forms, links, scripts)
- Use inline CSS styles for immediate application
- Consider modern design systems and component libraries for inspiration

ADDITIONAL GUIDANCE:
- If the user mentions specific design styles (e.g., "modern SaaS", "minimalist", "dark theme"), research current examples of those styles
- Apply appropriate color psychology and modern color palettes
- Use proper spacing ratios (8px grid system recommended)
- Implement modern typography scales and font pairings
- Add subtle animations and transitions where appropriate
- Ensure proper contrast ratios for accessibility

ORIGINAL HTML:
${html}

Please provide ONLY the complete redesigned HTML with improved styling. The HTML should be ready to replace the current page content directly. Do not include any explanations, thinking process, or markdown formatting - just return the pure HTML code.

Start your response with <!DOCTYPE html> and ensure it's a complete, valid HTML document with all styles inline.`;
}

// Extract HTML from Gemini response
function extractHTMLFromResponse(response) {
    // Remove any markdown code blocks
    let html = response.replace(/```html\s*/gi, '').replace(/```\s*$/gi, '');
    
    // Ensure it starts with DOCTYPE or html tag
    if (!html.trim().toLowerCase().startsWith('<!doctype') && 
        !html.trim().toLowerCase().startsWith('<html')) {
        // Try to find HTML content in the response
        const htmlMatch = html.match(/<!DOCTYPE html>[\s\S]*<\/html>/i) || 
                         html.match(/<html[\s\S]*<\/html>/i);
        if (htmlMatch) {
            html = htmlMatch[0];
        } else {
            return null;
        }
    }
    
    // Basic validation
    if (html.includes('<html') && html.includes('</html>')) {
        return html.trim();
    }
    
    return null;
}

// Extension installation handler
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('AI Website Redesigner extension installed');
        
        // Set default settings
        chrome.storage.sync.set({
            extensionVersion: '1.0.0',
            installDate: new Date().toISOString()
        });
    }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // This will open the popup, but we can add additional logic here if needed
    console.log('Extension icon clicked on tab:', tab.url);
});
