#!/usr/bin/env python3
"""
Simple script to create placeholder PNG icons for the Chrome extension.
Requires PIL (Pillow): pip install Pillow
"""

from PIL import Image, ImageDraw
import os

def create_icon(size, filename):
    """Create a simple icon with gradient background and magic wand symbol"""
    
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw gradient background circle
    # Create a simple gradient effect by drawing multiple circles
    center = size // 2
    radius = center - 1
    
    for i in range(radius):
        # Calculate color for gradient (purple to blue)
        ratio = i / radius
        r = int(102 + (118 - 102) * ratio)  # 102 -> 118
        g = int(126 + (75 - 126) * ratio)   # 126 -> 75  
        b = int(234 + (162 - 234) * ratio)  # 234 -> 162
        
        current_radius = radius - i
        draw.ellipse([center - current_radius, center - current_radius,
                     center + current_radius, center + current_radius],
                    fill=(r, g, b, 255))
    
    # Draw magic wand (simple line)
    wand_length = size // 3
    start_x = center - wand_length // 2
    start_y = center + wand_length // 2
    end_x = center + wand_length // 2
    end_y = center - wand_length // 2
    
    # Draw wand stick
    draw.line([start_x, start_y, end_x, end_y], fill=(255, 255, 255, 255), width=max(1, size // 16))
    
    # Draw star at wand tip (simplified)
    star_size = max(2, size // 8)
    star_x = end_x + star_size // 2
    star_y = end_y - star_size // 2
    
    # Simple star shape (diamond)
    star_points = [
        (star_x, star_y - star_size),
        (star_x + star_size, star_y),
        (star_x, star_y + star_size),
        (star_x - star_size, star_y)
    ]
    draw.polygon(star_points, fill=(255, 255, 255, 255))
    
    # Add small sparkles
    if size >= 32:
        sparkle_size = max(1, size // 16)
        # Sparkle 1
        s1_x, s1_y = center - size // 6, center - size // 12
        draw.ellipse([s1_x - sparkle_size, s1_y - sparkle_size,
                     s1_x + sparkle_size, s1_y + sparkle_size],
                    fill=(255, 255, 255, 255))
        
        # Sparkle 2
        s2_x, s2_y = center + size // 8, center + size // 6
        draw.ellipse([s2_x - sparkle_size, s2_y - sparkle_size,
                     s2_x + sparkle_size, s2_y + sparkle_size],
                    fill=(255, 255, 255, 255))
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    """Create all required icon sizes"""
    
    # Create icons directory if it doesn't exist
    icons_dir = 'icons'
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    # Icon sizes required by Chrome extensions
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = os.path.join(icons_dir, f'icon{size}.png')
        create_icon(size, filename)
    
    print("\nAll placeholder icons created successfully!")
    print("You can replace these with better icons later using the SVG file or icon generator.")

if __name__ == '__main__':
    main()
