// Content script for AI Website Redesigner Chrome Extension

// State management
let originalHTML = null;
let hasBackup = false;
let isRedesigned = false;

// Initialize content script
(function() {
    console.log('AI Website Redesigner content script loaded');
    
    // Store original HTML on load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', storeOriginalHTML);
    } else {
        storeOriginalHTML();
    }
})();

// Store original HTML for backup
function storeOriginalHTML() {
    if (!originalHTML) {
        originalHTML = document.documentElement.outerHTML;
        hasBackup = true;
        console.log('Original HTML stored for backup');
    }
}

// Message listener for communication with popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
        case 'checkStatus':
            sendResponse({
                success: true,
                hasBackup: hasBackup,
                isRedesigned: isRedesigned,
                url: window.location.href
            });
            break;

        case 'extractHTML':
            handleExtractHTML(sendResponse);
            break;

        case 'applyRedesign':
            handleApplyRedesign(message.html, sendResponse);
            break;

        case 'revertChanges':
            handleRevertChanges(sendResponse);
            break;

        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
    
    return true; // Keep message channel open for async responses
});

// Extract current HTML
function handleExtractHTML(sendResponse) {
    try {
        // Store current state if not already stored
        if (!originalHTML) {
            storeOriginalHTML();
        }

        // Get current HTML
        const currentHTML = document.documentElement.outerHTML;
        
        // Clean up the HTML for processing
        const cleanedHTML = cleanHTMLForExtraction(currentHTML);
        
        sendResponse({
            success: true,
            html: cleanedHTML,
            url: window.location.href,
            title: document.title
        });
        
        console.log('HTML extracted successfully');
    } catch (error) {
        console.error('Error extracting HTML:', error);
        sendResponse({
            success: false,
            error: 'Failed to extract HTML: ' + error.message
        });
    }
}

// Apply redesigned HTML
function handleApplyRedesign(newHTML, sendResponse) {
    try {
        // Validate the new HTML
        if (!newHTML || typeof newHTML !== 'string') {
            throw new Error('Invalid HTML provided');
        }

        // Store original if not already stored
        if (!originalHTML) {
            storeOriginalHTML();
        }

        // Create a temporary container to validate HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newHTML;
        
        // Basic validation - check if it contains essential elements
        if (!newHTML.includes('<html') || !newHTML.includes('</html>')) {
            throw new Error('Invalid HTML structure');
        }

        // Apply the redesign with smooth transition
        applyRedesignWithTransition(newHTML);
        
        isRedesigned = true;
        
        sendResponse({
            success: true,
            message: 'Redesign applied successfully'
        });
        
        console.log('Redesign applied successfully');
        
        // Show notification
        showNotification('Website redesigned successfully!', 'success');
        
    } catch (error) {
        console.error('Error applying redesign:', error);
        sendResponse({
            success: false,
            error: 'Failed to apply redesign: ' + error.message
        });
        
        showNotification('Failed to apply redesign', 'error');
    }
}

// Revert to original HTML
function handleRevertChanges(sendResponse) {
    try {
        if (!originalHTML) {
            throw new Error('No backup available');
        }

        // Apply original HTML with transition
        applyRedesignWithTransition(originalHTML);
        
        isRedesigned = false;
        
        sendResponse({
            success: true,
            message: 'Changes reverted successfully'
        });
        
        console.log('Changes reverted successfully');
        showNotification('Website reverted to original state', 'success');
        
    } catch (error) {
        console.error('Error reverting changes:', error);
        sendResponse({
            success: false,
            error: 'Failed to revert changes: ' + error.message
        });
        
        showNotification('Failed to revert changes', 'error');
    }
}

// Clean HTML for extraction
function cleanHTMLForExtraction(html) {
    // Remove extension-specific elements
    let cleaned = html.replace(/<div id="ai-redesigner-notification"[\s\S]*?<\/div>/gi, '');
    
    // Remove any temporary elements
    cleaned = cleaned.replace(/<div class="ai-redesigner-temp"[\s\S]*?<\/div>/gi, '');
    
    // Clean up whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    
    return cleaned;
}

// Apply redesign with smooth transition
function applyRedesignWithTransition(newHTML) {
    // Add fade out effect
    document.body.style.transition = 'opacity 0.3s ease';
    document.body.style.opacity = '0';
    
    setTimeout(() => {
        // Replace the entire document
        document.open();
        document.write(newHTML);
        document.close();
        
        // Add fade in effect
        setTimeout(() => {
            if (document.body) {
                document.body.style.transition = 'opacity 0.3s ease';
                document.body.style.opacity = '1';
                
                // Clean up transition styles after animation
                setTimeout(() => {
                    if (document.body) {
                        document.body.style.transition = '';
                    }
                }, 300);
            }
        }, 50);
        
        // Re-initialize content script after DOM replacement
        setTimeout(() => {
            initializeAfterRedesign();
        }, 100);
        
    }, 300);
}

// Initialize after redesign is applied
function initializeAfterRedesign() {
    // Re-attach event listeners if needed
    console.log('Content script re-initialized after redesign');
    
    // Ensure scroll behavior is smooth
    if (document.documentElement) {
        document.documentElement.style.scrollBehavior = 'smooth';
    }
    
    // Fix any broken links or forms if needed
    fixBrokenElements();
}

// Fix potentially broken elements after redesign
function fixBrokenElements() {
    // Fix relative URLs that might have broken
    const links = document.querySelectorAll('a[href^="/"], a[href^="./"], a[href^="../"]');
    links.forEach(link => {
        const href = link.getAttribute('href');
        if (href && !href.startsWith('http')) {
            // Convert relative URLs to absolute
            try {
                const absoluteURL = new URL(href, window.location.origin);
                link.setAttribute('href', absoluteURL.href);
            } catch (e) {
                console.warn('Could not fix relative URL:', href);
            }
        }
    });
    
    // Fix image sources
    const images = document.querySelectorAll('img[src^="/"], img[src^="./"], img[src^="../"]');
    images.forEach(img => {
        const src = img.getAttribute('src');
        if (src && !src.startsWith('http') && !src.startsWith('data:')) {
            try {
                const absoluteURL = new URL(src, window.location.origin);
                img.setAttribute('src', absoluteURL.href);
            } catch (e) {
                console.warn('Could not fix relative image URL:', src);
            }
        }
    });
}

// Show notification to user
function showNotification(message, type = 'info') {
    // Remove existing notification
    const existingNotification = document.getElementById('ai-redesigner-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.id = 'ai-redesigner-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Handle page navigation
window.addEventListener('beforeunload', () => {
    // Reset state on page navigation
    originalHTML = null;
    hasBackup = false;
    isRedesigned = false;
});

// Handle dynamic content changes
const observer = new MutationObserver((mutations) => {
    // Only observe if we haven't stored original HTML yet
    if (!originalHTML && document.readyState === 'complete') {
        storeOriginalHTML();
    }
});

// Start observing
if (document.body) {
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
} else {
    document.addEventListener('DOMContentLoaded', () => {
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
}
