/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    width: 380px;
    min-height: 500px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

.container {
    background: #ffffff;
    margin: 8px;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 24px;
    color: #ffd700;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.version {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Main Content */
.main-content {
    padding: 20px;
}

.section {
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.section-header i {
    color: #667eea;
    font-size: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

/* Input Groups */
.input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.input-group input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #f8fafc;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea-group {
    position: relative;
    margin-bottom: 16px;
}

.textarea-group textarea {
    width: 100%;
    min-height: 120px;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.2s ease;
    background: #f8fafc;
}

.textarea-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #718096;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    flex: 1;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

/* Options */
.options-group {
    margin-bottom: 20px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.option-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

.option-item label {
    font-size: 14px;
    color: #4a5568;
    cursor: pointer;
}

/* API Status */
.api-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    font-size: 13px;
    color: #0369a1;
}

.api-status.success {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.api-status.error {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.api-status a {
    color: inherit;
    text-decoration: underline;
}

/* Status Section */
.status-section {
    text-align: center;
    padding: 20px;
}

.loading-spinner {
    margin-bottom: 16px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-text {
    font-size: 14px;
    color: #4a5568;
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* Results Section */
.results-content {
    background: #f8fafc;
    border-radius: 8px;
    padding: 16px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item .label {
    font-size: 13px;
    color: #718096;
    font-weight: 500;
}

.result-item .value {
    font-size: 13px;
    color: #2d3748;
    font-weight: 600;
}

/* Footer */
.footer {
    padding: 16px 20px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer-links a {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #718096;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #667eea;
}

/* Animations */
.section {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Disabled state */
.btn-primary:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.btn-primary:disabled:hover {
    transform: none;
    box-shadow: none;
}
