// DOM Elements
const apiKeyInput = document.getElementById('apiKey');
const saveApiKeyBtn = document.getElementById('saveApiKey');
const apiStatus = document.getElementById('apiStatus');
const promptInput = document.getElementById('promptInput');
const charCount = document.getElementById('charCount');
const preserveContentCheckbox = document.getElementById('preserveContent');
const responsiveDesignCheckbox = document.getElementById('responsiveDesign');
const redesignBtn = document.getElementById('redesignBtn');
const revertBtn = document.getElementById('revertBtn');
const statusSection = document.getElementById('statusSection');
const statusText = document.getElementById('statusText');
const progressFill = document.getElementById('progressFill');
const resultsSection = document.getElementById('resultsSection');
const changesApplied = document.getElementById('changesApplied');
const processingTime = document.getElementById('processingTime');

// State
let isProcessing = false;
let startTime = null;

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
    await loadSavedApiKey();
    setupEventListeners();
    checkCurrentPageStatus();
});

// Load saved API key
async function loadSavedApiKey() {
    try {
        const result = await chrome.storage.sync.get(['geminiApiKey']);
        if (result.geminiApiKey) {
            apiKeyInput.value = result.geminiApiKey;
            updateApiStatus('success', 'API key saved and ready to use');
        }
    } catch (error) {
        console.error('Error loading API key:', error);
    }
}

// Setup event listeners
function setupEventListeners() {
    // API key save
    saveApiKeyBtn.addEventListener('click', saveApiKey);
    apiKeyInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') saveApiKey();
    });

    // Character count for prompt
    promptInput.addEventListener('input', updateCharCount);

    // Main action buttons
    redesignBtn.addEventListener('click', startRedesign);
    revertBtn.addEventListener('click', revertChanges);

    // Footer links
    document.getElementById('helpLink').addEventListener('click', showHelp);
    document.getElementById('settingsLink').addEventListener('click', showSettings);
}

// Save API key
async function saveApiKey() {
    const apiKey = apiKeyInput.value.trim();
    
    if (!apiKey) {
        updateApiStatus('error', 'Please enter a valid API key');
        return;
    }

    try {
        await chrome.storage.sync.set({ geminiApiKey: apiKey });
        updateApiStatus('success', 'API key saved successfully');
        
        // Test the API key
        const response = await chrome.runtime.sendMessage({
            action: 'testApiKey',
            apiKey: apiKey
        });

        if (response.success) {
            updateApiStatus('success', 'API key verified and ready to use');
        } else {
            updateApiStatus('error', 'Invalid API key. Please check and try again.');
        }
    } catch (error) {
        updateApiStatus('error', 'Error saving API key');
        console.error('Error saving API key:', error);
    }
}

// Update API status
function updateApiStatus(type, message) {
    apiStatus.className = `api-status ${type}`;
    apiStatus.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
}

// Update character count
function updateCharCount() {
    const count = promptInput.value.length;
    charCount.textContent = count;
    
    if (count > 500) {
        charCount.style.color = '#dc2626';
        promptInput.value = promptInput.value.substring(0, 500);
        charCount.textContent = '500';
    } else if (count > 400) {
        charCount.style.color = '#f59e0b';
    } else {
        charCount.style.color = '#718096';
    }
}

// Check current page status
async function checkCurrentPageStatus() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        // Test both content script and background script connections
        console.log('Testing connections...');

        // Test background script
        try {
            const bgResponse = await chrome.runtime.sendMessage({ action: 'ping' });
            console.log('Background script response:', bgResponse);
        } catch (error) {
            console.error('Background script connection failed:', error);
        }

        // Test content script
        try {
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'checkStatus' });
            console.log('Content script response:', response);

            if (response && response.hasBackup) {
                revertBtn.style.display = 'block';
            }
        } catch (error) {
            console.error('Content script connection failed:', error);
            // Content script might not be loaded yet, this is normal
        }
    } catch (error) {
        console.error('Error in checkCurrentPageStatus:', error);
    }
}

// Start redesign process
async function startRedesign() {
    const apiKey = apiKeyInput.value.trim();
    const prompt = promptInput.value.trim();

    // Validation
    if (!apiKey) {
        updateApiStatus('error', 'Please enter your Gemini API key first');
        return;
    }

    if (!prompt) {
        promptInput.focus();
        return;
    }

    if (isProcessing) return;

    isProcessing = true;
    startTime = Date.now();
    
    // Update UI
    redesignBtn.disabled = true;
    statusSection.style.display = 'block';
    resultsSection.style.display = 'none';
    
    try {
        // Get current tab
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab || !tab.id) {
            throw new Error('Could not access current tab');
        }

        // Check if we can access the tab
        if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') || tab.url.startsWith('edge://') || tab.url.startsWith('about:')) {
            throw new Error('Cannot redesign browser internal pages. Please navigate to a regular website.');
        }

        // Step 1: Extract HTML
        updateStatus('Extracting website content...', 20);
        let htmlResponse;
        try {
            htmlResponse = await chrome.tabs.sendMessage(tab.id, { action: 'extractHTML' });
        } catch (error) {
            console.error('Content script communication error:', error);
            if (error.message.includes('Could not establish connection') || error.message.includes('Receiving end does not exist')) {
                throw new Error('Content script not loaded. Please refresh the page and try again.');
            }
            throw new Error('Failed to communicate with page content: ' + error.message);
        }

        if (!htmlResponse || !htmlResponse.success) {
            throw new Error(htmlResponse?.error || 'Failed to extract website content');
        }

        if (!htmlResponse.html || htmlResponse.html.trim() === '') {
            throw new Error('No content found on this page');
        }

        // Step 2: Send to Gemini API
        updateStatus('Generating redesign with AI...', 50);
        let redesignResponse;
        try {
            redesignResponse = await chrome.runtime.sendMessage({
                action: 'redesignWithGemini',
                apiKey: apiKey,
                html: htmlResponse.html,
                prompt: prompt,
                options: {
                    preserveContent: preserveContentCheckbox.checked,
                    responsiveDesign: responsiveDesignCheckbox.checked
                }
            });
        } catch (error) {
            console.error('Background script communication error:', error);
            if (error.message.includes('Could not establish connection') || error.message.includes('Receiving end does not exist')) {
                throw new Error('Extension background script not responding. Please reload the extension.');
            }
            throw new Error('Failed to communicate with AI service: ' + error.message);
        }

        if (!redesignResponse || !redesignResponse.success) {
            throw new Error(redesignResponse?.error || 'Failed to generate redesign');
        }

        if (!redesignResponse.redesignedHTML || redesignResponse.redesignedHTML.trim() === '') {
            throw new Error('AI generated empty redesign. Please try a different prompt.');
        }

        // Step 3: Apply redesign
        updateStatus('Applying redesign to website...', 80);
        let applyResponse;
        try {
            applyResponse = await chrome.tabs.sendMessage(tab.id, {
                action: 'applyRedesign',
                html: redesignResponse.redesignedHTML
            });
        } catch (error) {
            console.error('Apply redesign communication error:', error);
            if (error.message.includes('Could not establish connection') || error.message.includes('Receiving end does not exist')) {
                throw new Error('Lost connection to page. The redesign was generated but could not be applied. Please refresh and try again.');
            }
            throw new Error('Failed to apply redesign: ' + error.message);
        }

        if (!applyResponse || !applyResponse.success) {
            throw new Error(applyResponse?.error || 'Failed to apply redesign');
        }

        // Step 4: Complete
        updateStatus('Redesign complete!', 100);

        setTimeout(() => {
            showResults(redesignResponse.changes || 'Website redesigned successfully');
            revertBtn.style.display = 'block';
        }, 1000);

    } catch (error) {
        console.error('Redesign error:', error);
        let errorMessage = error.message || 'An error occurred during redesign';

        // Provide more helpful error messages
        if (errorMessage.includes('Could not establish connection')) {
            errorMessage = 'Connection error: Please refresh the page and try again. Make sure you\'re on a regular website (not a browser internal page).';
        } else if (errorMessage.includes('API key')) {
            errorMessage = 'API key issue: ' + errorMessage;
        } else if (errorMessage.includes('Gemini API error')) {
            errorMessage = 'AI service error: ' + errorMessage;
        }

        updateApiStatus('error', errorMessage);
        statusSection.style.display = 'none';
    } finally {
        isProcessing = false;
        redesignBtn.disabled = false;
    }
}

// Update status during processing
function updateStatus(message, progress) {
    statusText.textContent = message;
    progressFill.style.width = `${progress}%`;
}

// Show results
function showResults(changes) {
    const processingTimeMs = Date.now() - startTime;
    const processingTimeText = `${(processingTimeMs / 1000).toFixed(1)}s`;
    
    statusSection.style.display = 'none';
    resultsSection.style.display = 'block';
    
    changesApplied.textContent = changes || 'Website redesigned successfully';
    processingTime.textContent = processingTimeText;
}

// Revert changes
async function revertChanges() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const response = await chrome.tabs.sendMessage(tab.id, { action: 'revertChanges' });
        
        if (response.success) {
            revertBtn.style.display = 'none';
            resultsSection.style.display = 'none';
            updateApiStatus('success', 'Website reverted to original state');
        } else {
            updateApiStatus('error', 'Failed to revert changes');
        }
    } catch (error) {
        console.error('Revert error:', error);
        updateApiStatus('error', 'Error reverting changes');
    }
}

// Show help
function showHelp() {
    chrome.tabs.create({
        url: 'https://github.com/your-repo/ai-website-redesigner#help'
    });
}

// Show settings
function showSettings() {
    // Could open a settings page or show inline settings
    alert('Settings panel coming soon!');
}

// Handle messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'updateStatus') {
        updateStatus(message.text, message.progress);
    }
});
